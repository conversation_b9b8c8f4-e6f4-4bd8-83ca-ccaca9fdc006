<template>
  <div>
    <PageHeader v-if="submitStreamID" :submitStreamID="submitStreamID" @back="goBack">
      <template #extra>
        <div class="flex items-center gap-20px">
          <Popconfirm
            title="确认操作？"
            okText="确认"
            cancelText="取消"
            @confirm="handleBulkUpdate"
          >
            <Button shape="round">
              <div class="flex items-center">
                <span>全部更新</span>
              </div>
            </Button>
          </Popconfirm>
          <Popconfirm
            title="确认操作？"
            okText="确认"
            cancelText="取消"
            @confirm="handleBulkRestart"
          >
            <Button shape="round">
              <div class="flex items-center">
                <span>全部重启</span>
              </div>
            </Button>
          </Popconfirm>
          <Button shape="round" @click="handleShowSchedule">
            <div class="flex items-center">
              <span>定时任务</span>
            </div>
          </Button>
          <Button shape="round" @click="handleEditRule">
            <div class="flex items-center">
              <span>实例分配规则</span>
            </div>
          </Button>
          <Button shape="round" @click="handleShowOperation">
            <div class="flex items-center">
              <span>操作记录</span>
            </div>
          </Button>
        </div>
      </template>
    </PageHeader>
    <div class="example-config-list">
      <div v-for="item in exampleConfig" :key="item.id" class="example-config-list-item mx-20px my-10px flex justify-between b-rd-8px p-20px bg-FO-Container-Fill1!">
        <div class="flex items-center">
          <DragOutlined class="list-drag-btn hidden h-10px w-10px cursor-grab" />
          <div class="list-status-dot h-10px w-10px b-rd-5px" :class="{ 'bg-FO-Datavis-Yellow2': item === ExampleStatus.Busy, 'bg-FO-Datavis-Blue2': item === ExampleStatus.Idle, 'bg-FO-Content-Text4': item === ExampleStatus.Offline, 'bg-FO-Functional-Error1-Default': item === ExampleStatus.Disabled }" />
          <div class="ml-10px">
            <div class="flex items-center gap-10px">
              <span class="FO-Font-B16">{{ item.name || '未命名' }}</span><Icon :icon="editIcon" class="cursor-pointer" @click="editExampleDetail" />
            </div>
            <div class="flex gap-20px c-FO-Content-Text3">
              <span>类型：非编译</span>
              <span>Workspace:asdf</span>
              <span>IP:127.0.0.1:3000</span>
            </div>
          </div>
        </div>

        <div class="flex items-center gap-10px">
          <div class="flex gap-10px">
            <span>实例以禁用</span>
            <span>更新中</span>
          </div>
          <div class="flex gap-10px">
            <Button @click="exampleUpdate">
              更新
            </Button>
            <Button @click="exampleRestart">
              重启
            </Button>
            <Button @click="exampleDisable">
              禁用
            </Button>
          </div>
        </div>
      </div>
    </div>
    <AssignRuleModalHolder />
    <ExampleDetailDrawerHolder />
    <OperationDrawerHolder />
    <ScheduleModalHolder />
  </div>
</template>

<script setup lang="ts">
import { DragOutlined } from '@ant-design/icons-vue';
import PageHeader from '../../PageHeader.vue';
import { useRouter } from 'vue-router';
import { Button, Popconfirm } from 'ant-design-vue';
import { PlatformEnterPoint } from '@hg-tech/oasis-common';
import { nextTick, onMounted, ref } from 'vue';
import { useSortable } from '../../../hooks/useSortable';
import { ExampleStatus } from '../../commit-center-home/steams.data';
import editIcon from '@iconify-icons/icon-park-outline/edit';
import { Icon } from '@iconify/vue';
import AssignRuleModal from './AssignRuleModal.vue';
import { useModalShow } from '@hg-tech/utils-vue';
import ExampleDetailDrawer from './ExampleDetailDrawer.vue';
import OperationDrawer from './OperationDrawer.vue';
import ScheduleModal from './ScheduleModal.vue';
import { TrackEventName } from '../../../constants/event';
import { traceClickEvent } from '../../../services/track';
import { type ExampleConfigItem, getConfigListApi } from '../../../api';
import { useForgeonConfigStore } from '../../../store/modules/forgeonConfig';
import { store } from '../../../store/pinia';

const router = useRouter();
const routeParams = router.currentRoute.value.params;
const forgeonConfig = useForgeonConfigStore(store);
const exampleConfig = ref<ExampleConfigItem[]>([]);
const [AssignRuleModalHolder, showAssignRuleModal] = useModalShow(AssignRuleModal);
const [ExampleDetailDrawerHolder, showExampleDetailDrawer] = useModalShow(ExampleDetailDrawer);
const [OperationDrawerHolder, showOperationRecordModal] = useModalShow(OperationDrawer);
const [ScheduleModalHolder, showScheduleModal] = useModalShow(ScheduleModal);
const submitStreamID = routeParams.submitStreamID ? Number(routeParams.submitStreamID) : null;
async function handleEditRule() {
  await showAssignRuleModal({});
  traceClickEvent(TrackEventName.SUBMIT_CENTER_INSTANCE_CONFIG_ALLOCATION_SAVE);
}
async function handleShowSchedule() {
  await showScheduleModal({});
}
function handleShowOperation() {
  traceClickEvent(TrackEventName.SUBMIT_CENTER_INSTANCE_CONFIG_OPERATION_LOG_CLICK);
  showOperationRecordModal({});
}
async function getExampleConfigList() {
  try {
    const res = await getConfigListApi({ id: forgeonConfig.currentProjectId!, streamID: submitStreamID! }, {});
    exampleConfig.value = res.data?.data || [{
      id: 4,
      ipAddress: 'http://*************:50134',
      instanceType: 0,
      workState: 1,
      pendingState: 0,
      disabled: false,
      name: '未命名',
      bootTimestamp: 1755522202,
    }, {
      id: 3,
      ipAddress: 'http://*************:65348',
      instanceType: 0,
      workState: 1,
      pendingState: 0,
      disabled: false,
      name: '未命名',
      bootTimestamp: 1755522202,
    }];
  } catch {
    exampleConfig.value = [{
      id: 4,
      ipAddress: 'http://*************:50134',
      instanceType: 0,
      workState: 1,
      pendingState: 0,
      disabled: false,
      name: '未命名',
      bootTimestamp: 1755522202,
    }, {
      id: 3,
      ipAddress: 'http://*************:65348',
      instanceType: 0,
      workState: 1,
      pendingState: 0,
      disabled: false,
      name: '未命名',
      bootTimestamp: 1755522202,
    }];
  }
}

function goBack() {
  router.push({
    name: PlatformEnterPoint.Example,
    params: {
      submitStreamID,
    },
  });
}

async function editExampleDetail() {
  await showExampleDetailDrawer({});
  traceClickEvent(TrackEventName.SUBMIT_CENTER_INSTANCE_CONFIG_EDIT_SAVE);
}
function handleBulkUpdate() {
  traceClickEvent(TrackEventName.SUBMIT_CENTER_INSTANCE_CONFIG_BULK_UPDATE_CLICK);
}
function handleBulkRestart() {
  traceClickEvent(TrackEventName.SUBMIT_CENTER_INSTANCE_CONFIG_BULK_RESTART_CLICK);
}
function exampleUpdate() {
  traceClickEvent(TrackEventName.SUBMIT_CENTER_INSTANCE_CONFIG_SINGLE_UPDATE_CLICK);
}
function exampleRestart() {
  traceClickEvent(TrackEventName.SUBMIT_CENTER_INSTANCE_CONFIG_SINGLE_RESTART_CLICK);
}
function exampleDisable() {
  traceClickEvent(TrackEventName.SUBMIT_CENTER_INSTANCE_CONFIG_SINGLE_DISABLE_CLICK);
}
// 初始化拖拽
function initDrag() {
  nextTick(() => {
    const el = document.querySelector(`.example-config-list`) as HTMLElement;
    useSortable(el, {
      handle: `.list-drag-btn`,
      onEnd: async ({ oldIndex, newIndex }: { oldIndex?: number; newIndex?: number }) => {
        console.log('🚀 ~ initDrag ~ oldIndex, newIndex:', oldIndex, newIndex);
      },
    });
  });
}
onMounted(async () => {
  await getExampleConfigList();
  initDrag();
});
</script>

<style lang="less" scoped>
.example-config-list {
  max-height: calc(100vh - 200px);
  overflow: auto;
  .example-config-list-item {
    &:hover {
      .list-drag-btn {
        display: block;
      }
      .list-status-dot {
        display: none;
      }
    }
  }
}
</style>
